import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import {
  BehaviorSubject,
  Observable,
  throwError,
  timer,
  EMPTY,
  of,
} from 'rxjs';
import {
  catchError,
  map,
  tap,
  switchMap,
  share,
  retry,
  delay,
  retryWhen,
  concatMap,
  finalize,
} from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import {
  User,
  CompleteUser,
  UserCredentials,
  RegistrationData,
  AuthResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
  EmailOtpRequest,
  EmailOtpVerify,
  PhoneOtpRequest,
  PhoneOtpVerify,
  ApiError,
} from '../../models/user.model';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser$: Observable<User | null>;
  private completeUserSubject: BehaviorSubject<CompleteUser | null>;
  public completeUser$: Observable<CompleteUser | null>;
  private accessTokenKey = 'access_token';
  private refreshTokenKey = 'refresh_token';
  private tokenTypeKey = 'token_type';
  private userKey = 'current_user';
  private completeUserKey = 'complete_user';
  private tokenExpiryKey = 'token_expiry';

  // Token refresh management
  private refreshTokenRequest$: Observable<AuthResponse> | null = null;
  private refreshTimer: any = null;

  // Request deduplication for user data fetching
  private userDataRequest$: Observable<CompleteUser> | null = null;
  private corsFailureCount = 0;
  private lastCorsFailureTime = 0;
  private readonly maxCorsRetries = 3;
  private readonly corsBackoffDelay = 5000; // 5 seconds

  constructor(private http: HttpClient) {
    this.currentUserSubject = new BehaviorSubject<User | null>(
      this.getUserFromStorage()
    );
    this.currentUser$ = this.currentUserSubject.asObservable();

    this.completeUserSubject = new BehaviorSubject<CompleteUser | null>(
      this.getCompleteUserFromStorage()
    );
    this.completeUser$ = this.completeUserSubject.asObservable();

    // Start token refresh timer if user is already authenticated
    if (this.isAuthenticated()) {
      console.log(
        'AuthService: User already authenticated, starting token refresh timer'
      );
      this.scheduleTokenRefresh();
    }
  }

  // Get current user value
  public getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  // Get complete user value
  public getCompleteUser(): CompleteUser | null {
    return this.completeUserSubject.value;
  }

  // Get raw user data from login response (includes type, defaultOrganization, etc.)
  public getRawUserData(): any {
    const userJson = localStorage.getItem(this.userKey);
    return userJson ? JSON.parse(userJson) : null;
  }

  // Check if user is authenticated (check both localStorage and cookies)
  public isAuthenticated(): boolean {
    const token = this.getAccessToken(); // This now checks both localStorage and cookies
    const isAuth = !!token;
    console.log('AuthService: isAuthenticated check:', {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      isAuthenticated: isAuth,
    });
    return isAuth;
  }

  // Detect if an error is a CORS error
  private isCorsError(error: any): boolean {
    return (
      error &&
      (error.message?.includes('CORS') ||
        error.message?.includes('Access-Control-Allow-Origin') ||
        error.status === 0 ||
        (error.error instanceof ProgressEvent && error.status === 0))
    );
  }

  // Check if we should skip API calls due to recent CORS failures
  private shouldSkipApiCallDueToCors(): boolean {
    const now = Date.now();
    const timeSinceLastFailure = now - this.lastCorsFailureTime;

    if (
      this.corsFailureCount >= this.maxCorsRetries &&
      timeSinceLastFailure < this.corsBackoffDelay
    ) {
      console.log(
        'AuthService: Skipping API call due to recent CORS failures',
        {
          corsFailureCount: this.corsFailureCount,
          timeSinceLastFailure,
          backoffDelay: this.corsBackoffDelay,
        }
      );
      return true;
    }

    // Reset failure count if enough time has passed
    if (timeSinceLastFailure >= this.corsBackoffDelay) {
      this.corsFailureCount = 0;
    }

    return false;
  }

  // Refresh complete user data from server with deduplication and CORS handling
  public refreshCompleteUserData(): Observable<CompleteUser> {
    console.log('AuthService: refreshCompleteUserData called');

    // If there's already a request in progress, return it
    if (this.userDataRequest$) {
      console.log(
        'AuthService: User data request already in progress, returning existing observable'
      );
      return this.userDataRequest$;
    }

    // Check if we should skip due to recent CORS failures
    if (this.shouldSkipApiCallDueToCors()) {
      // Try to return cached data if available
      const cachedUser = this.getCompleteUser();
      if (cachedUser) {
        console.log(
          'AuthService: Returning cached user data due to CORS backoff'
        );
        return of(cachedUser);
      } else {
        return throwError(
          () =>
            new Error('CORS failures detected, API calls temporarily disabled')
        );
      }
    }

    console.log('AuthService: Making fresh API call for user data');

    this.userDataRequest$ = this.http
      .get<CompleteUser>(`${environment.apiUrl}/users/me`)
      .pipe(
        tap((completeUser) => {
          console.log(
            'AuthService: Complete user data received:',
            completeUser
          );

          // Reset CORS failure count on success
          this.corsFailureCount = 0;
          this.lastCorsFailureTime = 0;

          // Store complete user data
          localStorage.setItem(
            this.completeUserKey,
            JSON.stringify(completeUser)
          );
          this.completeUserSubject.next(completeUser);

          // Also update the basic user data
          const basicUser: User = {
            _id: completeUser._id,
            JWT_UID: '', // This might need to be preserved from existing user
            email: completeUser.email,
            name: completeUser.name || completeUser.email, // Fallback to email if name is undefined
            phone_number: completeUser.phone_number,
            roles: [], // Convert from new role structure if needed
          };

          // Preserve existing JWT_UID if available
          const currentUser = this.getCurrentUser();
          if (currentUser?.JWT_UID) {
            basicUser.JWT_UID = currentUser.JWT_UID;
          }

          localStorage.setItem(this.userKey, JSON.stringify(basicUser));
          this.currentUserSubject.next(basicUser);
        }),
        catchError((error) => {
          console.error(
            'AuthService: Failed to refresh complete user data:',
            error
          );

          // Track CORS failures
          if (this.isCorsError(error)) {
            this.corsFailureCount++;
            this.lastCorsFailureTime = Date.now();
            console.log('AuthService: CORS error detected', {
              corsFailureCount: this.corsFailureCount,
              maxRetries: this.maxCorsRetries,
            });
          }

          return this.handleError(error);
        }),
        share(), // Share the observable to prevent multiple requests
        finalize(() => {
          // Clear the request reference when complete
          this.userDataRequest$ = null;
        })
      );

    return this.userDataRequest$;
  }

  // Load complete user data if authenticated with smart caching
  public loadCompleteUserData(): Observable<CompleteUser | null> {
    if (!this.isAuthenticated()) {
      return throwError(() => new Error('User not authenticated'));
    }

    // If we have recent CORS failures, try to use cached data first
    if (this.shouldSkipApiCallDueToCors()) {
      const cachedUser = this.getCompleteUser();
      if (cachedUser) {
        console.log('AuthService: Using cached user data due to CORS backoff');
        return of(cachedUser);
      }
    }

    // Otherwise, try to refresh from server
    return this.refreshCompleteUserData().pipe(
      catchError((error) => {
        // If API call fails but we have cached data, return cached data
        const cachedUser = this.getCompleteUser();
        if (cachedUser && this.isCorsError(error)) {
          console.log(
            'AuthService: API call failed due to CORS, falling back to cached data'
          );
          return of(cachedUser);
        }
        // If no cached data or non-CORS error, propagate the error
        throw error;
      })
    );
  }

  // Reset CORS failure tracking (useful for manual retry)
  public resetCorsFailureTracking(): void {
    console.log('AuthService: Resetting CORS failure tracking');
    this.corsFailureCount = 0;
    this.lastCorsFailureTime = 0;
  }

  // Get CORS failure status (for debugging)
  public getCorsFailureStatus(): {
    count: number;
    lastFailureTime: number;
    isInBackoff: boolean;
  } {
    const now = Date.now();
    const timeSinceLastFailure = now - this.lastCorsFailureTime;
    const isInBackoff =
      this.corsFailureCount >= this.maxCorsRetries &&
      timeSinceLastFailure < this.corsBackoffDelay;

    return {
      count: this.corsFailureCount,
      lastFailureTime: this.lastCorsFailureTime,
      isInBackoff,
    };
  }

  // Check if user has completed onboarding
  public isOnboardingCompleted(): boolean {
    const completeUser = this.getCompleteUser();
    return this.isOnboardingCompletedForUser(completeUser);
  }

  // Check if user has completed onboarding with fresh data
  public isOnboardingCompletedForUser(completeUser: any): boolean {
    if (!completeUser) return false;

    console.log('AuthService: Checking onboarding completion for user:', {
      userId: completeUser._id,
      type: completeUser.type,
      hasProfile: !!completeUser.profile,
    });

    // For individual users, check if required profile fields are completed
    if (completeUser.type === 'individual') {
      const profile = completeUser.profile;

      // Check all required fields for individual onboarding completion
      const hasFullName = !!profile?.fullName;
      const hasJobTitle = !!profile?.jobTitle;
      const hasCompanyName = !!profile?.companyName;
      const hasBio = !!profile?.bio;
      const hasIndustryTags = !!(
        profile?.industryTags && profile.industryTags.length > 0
      );
      const hasNetworkingGoal = !!profile?.networkingGoal;

      const isCompleted =
        hasFullName &&
        hasJobTitle &&
        hasCompanyName &&
        hasBio &&
        hasIndustryTags &&
        hasNetworkingGoal;

      console.log('AuthService: Individual user onboarding check:', {
        fullName: profile?.fullName,
        hasFullName,
        jobTitle: profile?.jobTitle,
        hasJobTitle,
        companyName: profile?.companyName,
        hasCompanyName,
        bio: profile?.bio,
        hasBio,
        industryTags: profile?.industryTags,
        hasIndustryTags,
        networkingGoal: profile?.networkingGoal,
        hasNetworkingGoal,
        isCompleted,
      });
      return isCompleted;
    }

    // For organization users, check if they have organization setup
    if (completeUser.type === 'organization') {
      // Check for organizations array (complete user data) or defaultOrganization (login response data)
      const hasOrganizations =
        (completeUser.organizations && completeUser.organizations.length > 0) ||
        (completeUser.defaultOrganization &&
          completeUser.defaultOrganization.subdomain);

      console.log('AuthService: Organization user onboarding check:', {
        organizationCount: completeUser.organizations?.length || 0,
        hasDefaultOrganization: !!completeUser.defaultOrganization,
        defaultOrgSubdomain: completeUser.defaultOrganization?.subdomain,
        hasOrganizations,
      });
      return hasOrganizations;
    }

    // If no type is set, onboarding is not completed
    console.log('AuthService: User has no type set, onboarding not completed');
    return false;
  }

  // Check if organization user has completed onboarding using login response data
  public isOrganizationOnboardingCompletedFromLoginData(
    loginUserData: any
  ): boolean {
    if (!loginUserData || loginUserData.type !== 'organization') {
      return false;
    }

    // For organization users, having a defaultOrganization with subdomain indicates completed onboarding
    const hasDefaultOrganization =
      loginUserData.defaultOrganization &&
      loginUserData.defaultOrganization.subdomain;

    console.log('AuthService: Organization onboarding check from login data:', {
      hasDefaultOrganization,
      defaultOrganization: loginUserData.defaultOrganization,
      userType: loginUserData.type,
    });

    return hasDefaultOrganization;
  }

  // Login with email/phone and password
  public login(credentials: UserCredentials): Observable<User> {
    console.log('AuthService: Starting login with credentials:', {
      identifier: credentials.identifier,
      hasPassword: !!credentials.password,
    });

    return this.http
      .post<AuthResponse>(`${environment.apiUrl}/auth/login`, credentials)
      .pipe(
        tap((response) => {
          console.log('AuthService: Login API response received:', {
            hasUser: !!response.user,
            hasIdToken: !!response.idToken,
            hasAccessToken: !!response.access_token,
            hasRefreshToken: !!response.refreshToken,
            hasRefreshTokenAlt: !!response.refresh_token,
            tokenType: response.token_type,
            userEmail: response.user?.email,
            responseKeys: Object.keys(response),
          });
          this.handleAuthentication(response);
        }),
        map((response) => response.user),
        catchError((error) => {
          console.error('AuthService: Login failed:', error);
          return this.handleError(error);
        })
      );
  }

  // Register new user
  public register(data: RegistrationData): Observable<User> {
    return this.http
      .post<AuthResponse>(`${environment.apiUrl}/users/register`, data)
      .pipe(
        tap((response) => this.handleAuthentication(response)),
        map((response) => response.user),
        catchError(this.handleError)
      );
  }

  // Request password reset
  public requestPasswordReset(email: string): Observable<{ message: string }> {
    const request: PasswordResetRequest = { email };
    return this.http
      .post<{ message: string }>(
        `${environment.apiUrl}/auth/request-reset`,
        request
      )
      .pipe(catchError(this.handleError));
  }

  // Reset password with token
  public resetPassword(
    token: string,
    newPassword: string
  ): Observable<{ message: string }> {
    const request: PasswordResetConfirm = { token, newPassword };
    return this.http
      .post<{ message: string }>(
        `${environment.apiUrl}/auth/reset-password`,
        request
      )
      .pipe(catchError(this.handleError));
  }

  // Email OTP Authentication
  public sendEmailOtp(email: string): Observable<{ message: string }> {
    const request: EmailOtpRequest = { email };
    return this.http
      .post<{ message: string }>(
        `${environment.apiUrl}/oauth/email/send-otp`,
        request
      )
      .pipe(catchError(this.handleError));
  }

  public verifyEmailOtp(email: string, code: string): Observable<AuthResponse> {
    const request: EmailOtpVerify = { email, code };
    return this.http
      .post<AuthResponse>(
        `${environment.apiUrl}/oauth/email/verify-otp`,
        request
      )
      .pipe(
        tap((response) => this.handleAuthentication(response)),
        catchError(this.handleError)
      );
  }

  // Phone OTP Authentication
  public sendPhoneOtp(
    phoneNumber: string,
    recaptchaToken: string
  ): Observable<{ sessionInfo: string }> {
    const request: PhoneOtpRequest = { phoneNumber, recaptchaToken };
    return this.http
      .post<{ sessionInfo: string }>(
        `${environment.apiUrl}/oauth/phone/send-code`,
        request
      )
      .pipe(catchError(this.handleError));
  }

  public verifyPhoneOtp(
    sessionInfo: string,
    code: string
  ): Observable<AuthResponse> {
    const request: PhoneOtpVerify = { sessionInfo, code };
    return this.http
      .post<AuthResponse>(
        `${environment.apiUrl}/oauth/phone/verify-code`,
        request
      )
      .pipe(
        tap((response) => this.handleAuthentication(response)),
        catchError(this.handleError)
      );
  }

  // Logout
  public logout(): void {
    console.log('AuthService: Starting logout process');

    // Call backend logout API to clear server-side session
    this.callBackendLogout().subscribe({
      next: () => {
        console.log('AuthService: Backend logout successful');
      },
      error: (error) => {
        console.warn(
          'AuthService: Backend logout failed, continuing with client logout:',
          error
        );
      },
      complete: () => {
        this.performClientLogout();
      },
    });
  }

  /**
   * Call backend logout API to clear server-side session
   */
  private callBackendLogout(): Observable<any> {
    const token = this.getAccessToken();
    if (!token) {
      console.log('AuthService: No token available for backend logout');
      return new Observable((observer) => {
        observer.next(null);
        observer.complete();
      });
    }

    console.log(
      'AuthService: Calling backend logout API with all_devices=true'
    );

    // Use the actual logout API endpoint with all_devices parameter
    // This will invalidate all user sessions and clear the token blacklist
    return this.http
      .post(`${environment.apiUrl}/auth/logout?all_devices=true`, {})
      .pipe(
        tap((response) => {
          console.log('AuthService: Backend logout API response:', response);
        }),
        catchError((error) => {
          console.warn('AuthService: Backend logout API error:', error);
          // Don't throw error, just continue with client logout
          return new Observable((observer) => {
            observer.next(null);
            observer.complete();
          });
        })
      );
  }

  /**
   * Perform client-side logout (clear tokens and data)
   */
  private performClientLogout(): void {
    console.log('AuthService: Performing client-side logout');

    // Clear token refresh timer
    this.clearTokenRefreshTimer();

    localStorage.removeItem(this.accessTokenKey);
    localStorage.removeItem(this.refreshTokenKey);
    localStorage.removeItem(this.tokenTypeKey);
    localStorage.removeItem(this.userKey);
    localStorage.removeItem(this.completeUserKey);
    localStorage.removeItem(this.tokenExpiryKey);

    // Clear cookies for subdomain logout
    this.deleteCookie('idToken');
    this.deleteCookie('refreshToken');

    this.currentUserSubject.next(null);
    this.completeUserSubject.next(null);

    console.log(
      'AuthService: User logged out, tokens cleared from localStorage and cookies'
    );
  }

  /**
   * Immediate logout without backend API call (for emergency situations)
   */
  public logoutImmediate(): void {
    console.log('AuthService: Performing immediate logout');
    this.performClientLogout();
  }

  /**
   * Force logout with backend session clearing (for debugging/admin purposes)
   * This method ensures all user sessions are invalidated on the backend
   */
  public forceLogoutAllSessions(): Observable<any> {
    console.log('AuthService: Force logout - clearing all user sessions');

    return this.callBackendLogout().pipe(
      tap(() => {
        this.performClientLogout();
        this.performComprehensiveCleanup();
      }),
      catchError((error) => {
        console.error('AuthService: Force logout failed:', error);
        // Still perform client logout even if backend fails
        this.performClientLogout();
        this.performComprehensiveCleanup();
        return throwError(() => error);
      })
    );
  }

  /**
   * Test API call with current token (for debugging)
   */
  public testApiCall(): Observable<any> {
    console.log('AuthService: Testing API call with current token');

    const token = this.getAccessToken();
    const tokenType = this.getTokenType();

    console.log('AuthService: Test API call details:', {
      hasToken: !!token,
      tokenLength: token?.length,
      tokenType,
      tokenPreview: token?.substring(0, 30) + '...',
    });

    return this.http.get(`${environment.apiUrl}/users/me`).pipe(
      tap((response) => {
        console.log('AuthService: Test API call successful:', response);
      }),
      catchError((error) => {
        console.error('AuthService: Test API call failed:', error);
        console.error('AuthService: Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          error: error.error,
        });
        return throwError(() => error);
      })
    );
  }

  /**
   * Test API call with manual headers (exactly like curl)
   */
  public testApiCallManual(): Observable<any> {
    console.log('AuthService: Testing API call with manual headers');

    const token = this.getAccessToken();
    const tokenType = this.getTokenType() || 'Bearer';

    // Normalize token type to match curl (capitalize first letter)
    const normalizedTokenType =
      tokenType.charAt(0).toUpperCase() + tokenType.slice(1).toLowerCase();

    console.log('AuthService: Manual API call details:', {
      hasToken: !!token,
      tokenLength: token?.length,
      originalTokenType: tokenType,
      normalizedTokenType,
      tokenPreview: token?.substring(0, 30) + '...',
    });

    const headers = {
      Accept: 'application/json',
      Authorization: `${normalizedTokenType} ${token}`,
    };

    console.log('AuthService: Manual headers:', headers);

    return this.http.get(`${environment.apiUrl}/users/me`, { headers }).pipe(
      tap((response) => {
        console.log('AuthService: Manual API call successful:', response);
      }),
      catchError((error) => {
        console.error('AuthService: Manual API call failed:', error);
        console.error('AuthService: Manual error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          error: error.error,
        });
        return throwError(() => error);
      })
    );
  }

  /**
   * Cross-domain logout for subdomain users
   * This method handles logout from subdomains by redirecting to main domain
   * with a logout flag to ensure tokens are cleared from both domains
   */
  public logoutFromSubdomain(): void {
    console.log('AuthService: Initiating cross-domain logout from subdomain');

    // Call backend logout API to invalidate all sessions before redirecting
    this.callBackendLogout().subscribe({
      next: () => {
        console.log(
          'AuthService: Backend logout completed, proceeding with cross-domain logout'
        );
      },
      error: (error) => {
        console.warn(
          'AuthService: Backend logout failed, continuing anyway:',
          error
        );
      },
      complete: () => {
        // Clear tokens from current subdomain
        this.performClientLogout();

        // Redirect to main domain with logout completion flag
        const protocol = window.location.protocol;
        const mainDomain = `${protocol}//${environment.appDomain}`;
        const logoutUrl = `${mainDomain}/auth/logout-complete`;

        console.log(
          'AuthService: Redirecting to main domain for logout completion:',
          logoutUrl
        );
        window.location.href = logoutUrl;
      },
    });
  }

  /**
   * Complete logout process on main domain
   * This method is called when user is redirected from subdomain logout
   */
  public completeLogoutFromSubdomain(): void {
    console.log('AuthService: Completing cross-domain logout on main domain');

    // For cross-domain logout, perform immediate client logout without backend call
    // since the backend logout was already called from the subdomain
    this.performClientLogout();

    // Additional comprehensive cleanup for cross-domain logout
    this.performComprehensiveCleanup();

    console.log('AuthService: Cross-domain logout completed successfully');
  }

  /**
   * Perform comprehensive cleanup of all stored data
   */
  private performComprehensiveCleanup(): void {
    console.log('AuthService: Performing comprehensive cleanup');

    // Clear any subdomain-related data that might be stored
    const keysToRemove = [
      'lastSubdomain',
      'lastOrganization',
      'selectedOrganization',
      'currentOrganization',
      'organizationData',
      'userPreferences',
      'dashboardState',
      'navigationState',
      // Clear any potential token variations
      'idToken',
      'refreshToken',
      'accessToken',
      'authToken',
      'token',
      // Clear any cached user data variations
      'userData',
      'userInfo',
      'profile',
      'completeUserData',
    ];

    keysToRemove.forEach((key) => {
      try {
        localStorage.removeItem(key);
        console.log(`AuthService: Cleared localStorage key: ${key}`);
      } catch (error) {
        console.warn(
          `AuthService: Failed to clear localStorage key ${key}:`,
          error
        );
      }
    });

    // Clear all cookies with different domain variations
    this.clearAllAuthCookies();

    console.log('AuthService: Comprehensive cleanup completed');
  }

  /**
   * Clear all authentication-related cookies
   */
  private clearAllAuthCookies(): void {
    const cookiesToClear = [
      'idToken',
      'refreshToken',
      'accessToken',
      'authToken',
    ];
    const domains = [
      window.location.hostname,
      `.${window.location.hostname}`,
      '.digimeet.live',
      'digimeet.live',
      '.localhost',
      'localhost',
    ];

    cookiesToClear.forEach((cookieName) => {
      domains.forEach((domain) => {
        try {
          // Clear cookie for each domain variation
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=${domain}; path=/;`;
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=${domain}; path=/; secure;`;
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=${domain}; path=/; samesite=lax;`;
        } catch (error) {
          // Ignore cookie clearing errors
        }
      });
    });

    console.log('AuthService: All authentication cookies cleared');
  }

  // Get access token (check localStorage first, then cookies)
  public getAccessToken(): string | null {
    let token = localStorage.getItem(this.accessTokenKey);
    if (!token) {
      token = this.getCookie('idToken');
      if (token) {
        // If found in cookie, also store in localStorage for consistency
        localStorage.setItem(this.accessTokenKey, token);
        console.log('AuthService: Retrieved access token from cookie');
      }
    }
    return token;
  }

  // Get refresh token (check localStorage first, then cookies)
  public getRefreshToken(): string | null {
    let token = localStorage.getItem(this.refreshTokenKey);
    if (!token) {
      token = this.getCookie('refreshToken');
      if (token) {
        // If found in cookie, also store in localStorage for consistency
        localStorage.setItem(this.refreshTokenKey, token);
        console.log('AuthService: Retrieved refresh token from cookie');
      }
    }
    return token;
  }

  // Get token type
  public getTokenType(): string | null {
    return localStorage.getItem(this.tokenTypeKey);
  }

  // Handle authentication response
  private handleAuthentication(response: AuthResponse): void {
    const { user } = response;

    // Handle both new and legacy token formats
    // Priority: new format first, then legacy format
    const accessToken = response.idToken || response.access_token;
    const refreshToken = response.refreshToken || response.refresh_token;
    // Normalize token type to proper case
    const rawTokenType = response.token_type || 'Bearer';
    const tokenType =
      rawTokenType.charAt(0).toUpperCase() +
      rawTokenType.slice(1).toLowerCase();

    console.log('AuthService: Token extraction details:', {
      responseIdToken: !!response.idToken,
      responseAccessToken: !!response.access_token,
      responseRefreshToken: !!response.refreshToken,
      responseRefreshTokenAlt: !!response.refresh_token,
      extractedAccessToken: !!accessToken,
      extractedRefreshToken: !!refreshToken,
      finalTokenType: tokenType,
    });

    console.log('AuthService: handleAuthentication called with response:', {
      hasUser: !!user,
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
      tokenType,
      accessTokenLength: accessToken?.length,
      refreshTokenLength: refreshToken?.length,
    });

    if (!accessToken || !refreshToken) {
      console.error('AuthService: Missing tokens in authentication response:', {
        accessToken: !!accessToken,
        refreshToken: !!refreshToken,
        response,
      });
      throw new Error('Invalid authentication response: missing tokens');
    }

    // Store tokens and user in local storage
    localStorage.setItem(this.accessTokenKey, accessToken);
    localStorage.setItem(this.refreshTokenKey, refreshToken);
    localStorage.setItem(this.tokenTypeKey, tokenType);
    localStorage.setItem(this.userKey, JSON.stringify(user));

    // Store tokens in cookies for subdomain sharing
    this.setCookieForSubdomain('idToken', accessToken);
    this.setCookieForSubdomain('refreshToken', refreshToken);

    console.log('AuthService: Tokens stored successfully:', {
      accessTokenKey: this.accessTokenKey,
      refreshTokenKey: this.refreshTokenKey,
      storedAccessToken: !!localStorage.getItem(this.accessTokenKey),
      storedRefreshToken: !!localStorage.getItem(this.refreshTokenKey),
      cookieIdToken: !!this.getCookie('idToken'),
      cookieRefreshToken: !!this.getCookie('refreshToken'),
    });

    // Update current user subject
    this.currentUserSubject.next(user);

    // Start automatic token refresh
    this.scheduleTokenRefresh();

    // Verify token is accessible before making API call
    const verifyToken = this.getAccessToken();
    console.log('AuthService: Token verification before API call:', {
      hasToken: !!verifyToken,
      tokenLength: verifyToken?.length,
      tokenPreview: verifyToken?.substring(0, 20) + '...',
    });

    // Try to decode JWT token to check its validity
    if (verifyToken) {
      try {
        const tokenParts = verifyToken.split('.');
        if (tokenParts.length === 3) {
          const payload = JSON.parse(atob(tokenParts[1]));
          const now = Math.floor(Date.now() / 1000);
          console.log('AuthService: JWT token payload:', {
            exp: payload.exp,
            iat: payload.iat,
            currentTime: now,
            isExpired: payload.exp < now,
            expiresIn: payload.exp - now,
            userId: payload.sub || payload.user_id || payload.id,
            email: payload.email,
          });
        } else {
          console.warn('AuthService: Token is not a valid JWT format');
        }
      } catch (error) {
        console.warn('AuthService: Failed to decode JWT token:', error);
      }
    }

    // Note: We no longer automatically fetch complete user data here
    // The login component will handle this and deal with CORS errors appropriately
    console.log('AuthService: Authentication completed successfully');
    console.log(
      'AuthService: Login component will handle user data fetching and routing'
    );
  }

  // Get user from storage
  private getUserFromStorage(): User | null {
    const userJson = localStorage.getItem(this.userKey);
    return userJson ? JSON.parse(userJson) : null;
  }

  // Get complete user from storage
  private getCompleteUserFromStorage(): CompleteUser | null {
    const completeUserJson = localStorage.getItem(this.completeUserKey);
    return completeUserJson ? JSON.parse(completeUserJson) : null;
  }

  // Handle API errors
  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'An error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = error.error.message;
    } else {
      // Server-side error
      const apiError = error.error as ApiError;
      if (apiError.error) {
        errorMessage = apiError.error;
      } else if (apiError.message) {
        errorMessage = apiError.message;
      } else if (apiError.details) {
        errorMessage = apiError.details;
      } else {
        errorMessage = error.statusText;
      }
    }

    return throwError(() => new Error(errorMessage));
  }

  // Token refresh functionality
  public refreshToken(): Observable<AuthResponse> {
    const refreshToken = this.getRefreshToken();

    if (!refreshToken) {
      console.error('AuthService: No refresh token available');
      this.logout();
      return throwError(() => new Error('No refresh token available'));
    }

    // If there's already a refresh request in progress, return it
    if (this.refreshTokenRequest$) {
      console.log('AuthService: Refresh token request already in progress');
      return this.refreshTokenRequest$;
    }

    console.log('AuthService: Refreshing access token...');

    this.refreshTokenRequest$ = this.http
      .post<AuthResponse>(`${environment.apiUrl}/auth/refresh`, {
        refreshToken,
      })
      .pipe(
        tap((response) => {
          console.log('AuthService: Token refresh successful');
          this.handleAuthentication(response);
          this.scheduleTokenRefresh();
        }),
        catchError((error) => {
          console.error('AuthService: Token refresh failed:', error);
          this.logout();
          return throwError(() => new Error('Token refresh failed'));
        }),
        share() // Share the observable to prevent multiple requests
      );

    // Clear the request after completion
    this.refreshTokenRequest$.subscribe({
      complete: () => {
        this.refreshTokenRequest$ = null;
      },
      error: () => {
        this.refreshTokenRequest$ = null;
      },
    });

    return this.refreshTokenRequest$;
  }

  private scheduleTokenRefresh(): void {
    // Clear existing timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    // Schedule refresh 5 minutes before expiry (55 minutes for 1-hour tokens)
    const refreshInterval = 55 * 60 * 1000; // 55 minutes in milliseconds

    console.log(
      `AuthService: Scheduling token refresh in ${
        refreshInterval / 1000 / 60
      } minutes`
    );

    this.refreshTimer = setTimeout(() => {
      console.log('AuthService: Auto-refreshing token...');
      this.refreshToken().subscribe({
        next: () => {
          console.log('AuthService: Auto token refresh successful');
        },
        error: (error) => {
          console.error('AuthService: Auto token refresh failed:', error);
        },
      });
    }, refreshInterval);
  }

  private clearTokenRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  // Check if token needs refresh (call this before API requests)
  public ensureValidToken(): Observable<string | null> {
    const token = this.getAccessToken();

    if (!token) {
      return throwError(() => new Error('No access token available'));
    }

    // For now, we'll refresh proactively since we don't have expiry info
    // In a real app, you'd decode the JWT to check expiry
    return new Observable((observer) => {
      observer.next(token);
      observer.complete();
    });
  }

  // Cookie methods for subdomain authentication
  private setCookieForSubdomain(name: string, value: string): void {
    // Set cookie with domain scope for subdomain sharing
    const domain = this.getCookieDomain();
    const expires = new Date();
    expires.setTime(expires.getTime() + 24 * 60 * 60 * 1000); // 24 hours

    const cookieString = `${name}=${value}; expires=${expires.toUTCString()}; domain=${domain}; path=/; SameSite=Lax`;
    document.cookie = cookieString;

    console.log(`AuthService: Set cookie ${name} for domain ${domain}`);
  }

  private getCookieDomain(): string {
    const hostname = window.location.hostname;

    // For localhost development, use .localhost
    if (hostname.includes('localhost')) {
      return '.localhost';
    }

    // For production, use .digimeet.live
    if (hostname.includes('digimeet.live')) {
      return '.digimeet.live';
    }

    // Fallback to current hostname
    return hostname;
  }

  private getCookie(name: string): string | null {
    const nameEQ = name + '=';
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  private deleteCookie(name: string): void {
    const domain = this.getCookieDomain();
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=${domain}; path=/;`;
  }
}
