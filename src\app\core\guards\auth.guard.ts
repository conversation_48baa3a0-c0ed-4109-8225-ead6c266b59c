import { Injectable } from '@angular/core';
import {
  Router,
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth/auth.service';
import { UserDataService } from '../services/user-data/user-data.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private authService: AuthService,
    private userDataService: UserDataService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    const isAuthenticated = this.authService.isAuthenticated();
    const requestedUrl = state.url;

    console.log('AuthGuard: Checking access to:', requestedUrl, {
      isAuthenticated,
      route: route.routeConfig?.path,
    });

    if (!isAuthenticated) {
      // User is not logged in, redirect to login page with return URL
      console.log('AuthGuard: User not authenticated, redirecting to login');
      this.router.navigate(['/auth/login'], {
        queryParams: { returnUrl: requestedUrl },
      });
      return false;
    }

    // User is authenticated, check if they're accessing the correct dashboard
    return this.userDataService.loadCompleteUserData().pipe(
      map((completeUser) => {
        if (!completeUser) {
          console.log('AuthGuard: No user data found');
          return true; // Allow access, let other guards handle it
        }

        // Check onboarding completion first
        if (!this.authService.isOnboardingCompletedForUser(completeUser)) {
          if (requestedUrl.startsWith('/onboarding')) {
            console.log(
              'AuthGuard: User accessing onboarding, allowing access'
            );
            return true;
          } else {
            console.log(
              'AuthGuard: Onboarding not completed, redirecting to onboarding'
            );
            this.router.navigate(['/onboarding']);
            return false;
          }
        }

        // Onboarding is completed, check if user is accessing correct dashboard
        const userType = completeUser.type;
        const isIndividual = userType === 'individual';
        const isOrganization = userType === 'organization';

        console.log('AuthGuard: User type routing check:', {
          userType,
          requestedUrl,
          isIndividual,
          isOrganization,
        });

        // Individual users should only access /dashboard
        if (isIndividual) {
          if (
            requestedUrl.startsWith('/dashboard') &&
            !requestedUrl.startsWith('/org-dashboard')
          ) {
            console.log(
              'AuthGuard: Individual user accessing correct dashboard'
            );
            return true;
          } else if (requestedUrl.startsWith('/org-dashboard')) {
            console.log(
              'AuthGuard: Individual user trying to access org dashboard, redirecting'
            );
            this.router.navigate(['/dashboard']);
            return false;
          }
        }

        // Organization users should only access /org-dashboard
        if (isOrganization) {
          if (requestedUrl.startsWith('/org-dashboard')) {
            console.log(
              'AuthGuard: Organization user accessing correct dashboard'
            );
            return true;
          } else if (
            requestedUrl.startsWith('/dashboard') &&
            !requestedUrl.startsWith('/org-dashboard')
          ) {
            console.log(
              'AuthGuard: Organization user trying to access individual dashboard, redirecting'
            );
            this.router.navigate(['/org-dashboard']);
            return false;
          }
        }

        // For other routes (admin, etc.), allow access
        console.log('AuthGuard: Allowing access to other routes');
        return true;
      }),
      catchError((error) => {
        console.error('AuthGuard: Error loading user data:', error);

        // If this is a CORS error and we have cached user data, try to use it
        const cachedUser = this.authService.getCompleteUser();
        if (cachedUser && this.isCorsError(error)) {
          console.log(
            'AuthGuard: CORS error detected, using cached user data for routing'
          );

          // Check onboarding completion first
          if (!this.authService.isOnboardingCompletedForUser(cachedUser)) {
            if (requestedUrl.startsWith('/onboarding')) {
              console.log(
                'AuthGuard: User accessing onboarding, allowing access'
              );
              return of(true);
            } else {
              console.log(
                'AuthGuard: Onboarding not completed, redirecting to onboarding'
              );
              this.router.navigate(['/onboarding']);
              return of(false);
            }
          }

          // Handle routing based on cached user type
          const userType = cachedUser.type;
          const isIndividual = userType === 'individual';
          const isOrganization = userType === 'organization';

          console.log('AuthGuard: Using cached data for user type routing:', {
            userType,
            requestedUrl,
            isIndividual,
            isOrganization,
          });

          // Individual users should only access /dashboard
          if (isIndividual) {
            if (
              requestedUrl.startsWith('/dashboard') &&
              !requestedUrl.startsWith('/org-dashboard')
            ) {
              return of(true);
            } else if (requestedUrl.startsWith('/org-dashboard')) {
              this.router.navigate(['/dashboard']);
              return of(false);
            }
          }

          // Organization users should only access /org-dashboard
          if (isOrganization) {
            if (requestedUrl.startsWith('/org-dashboard')) {
              return of(true);
            } else if (
              requestedUrl.startsWith('/dashboard') &&
              !requestedUrl.startsWith('/org-dashboard')
            ) {
              this.router.navigate(['/org-dashboard']);
              return of(false);
            }
          }
        }

        // On error without cached data, allow access but let other guards handle it
        return of(true);
      })
    );
  }

  private isCorsError(error: any): boolean {
    return (
      error &&
      (error.message?.includes('CORS') ||
        error.message?.includes('Access-Control-Allow-Origin') ||
        error.status === 0 ||
        (error.error instanceof ProgressEvent && error.status === 0))
    );
  }
}
