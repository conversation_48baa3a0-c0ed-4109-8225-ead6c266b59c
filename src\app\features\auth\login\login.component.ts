import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../../../core/services/auth/auth.service';
import { UserDataService } from '../../../core/services/user-data/user-data.service';
import { AlertType } from '../../../shared/components/alert/alert.component';
import { environment } from '../../../../environments/environment';

type AuthMethod = 'password' | 'otp';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  loading = false;
  submitted = false;
  returnUrl: string = '/dashboard';
  error: string = '';
  showAlert = false;
  alertType: AlertType = 'error';
  alertMessage: string = '';

  // Authentication method and OTP
  authMethod: AuthMethod = 'password';
  otpSent = false;
  otpRequestId: string = '';

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private userDataService: UserDataService
  ) {
    // Check if this is a redirect from subdomain logout
    const fromLogout = this.route.snapshot.queryParams['fromLogout'];

    if (fromLogout === 'true') {
      // User was redirected from subdomain logout, ensure complete logout
      console.log(
        'LoginComponent: Detected redirect from subdomain logout, ensuring clean state'
      );
      this.authService.completeLogoutFromSubdomain();
    } else {
      // Normal login flow - redirect to dashboard if already logged in
      if (this.authService.isAuthenticated()) {
        this.router.navigate(['/dashboard']);
      }
    }
  }

  ngOnInit(): void {
    this.loginForm = this.formBuilder.group({
      identifier: ['', [Validators.required]], // Can be email or phone
      password: [''],
      otp: [''],
      rememberMe: [false],
    });

    // Get return URL from route parameters or default to '/dashboard'
    this.returnUrl =
      this.route.snapshot.queryParams['returnUrl'] || '/dashboard';

    this.updateValidators();
  }

  // Convenience getter for easy access to form fields
  get f() {
    return this.loginForm.controls;
  }

  private updateValidators() {
    // Clear all validators first
    Object.keys(this.loginForm.controls).forEach((key) => {
      this.loginForm.get(key)?.clearValidators();
    });

    // Set validators based on auth method
    this.loginForm.get('identifier')?.setValidators([Validators.required]);

    if (this.authMethod === 'password') {
      this.loginForm.get('password')?.setValidators([Validators.required]);
    } else if (this.authMethod === 'otp') {
      if (this.otpSent) {
        this.loginForm
          .get('otp')
          ?.setValidators([Validators.required, Validators.minLength(6)]);
      }
    }

    // Update form validation
    Object.keys(this.loginForm.controls).forEach((key) => {
      this.loginForm.get(key)?.updateValueAndValidity();
    });
  }

  setAuthMethod(method: AuthMethod) {
    this.authMethod = method;
    this.showAlert = false;
    this.otpSent = false;
    this.updateValidators();
  }

  getSubmitButtonText(): string {
    if (this.authMethod === 'otp' && !this.otpSent) {
      return 'Send OTP';
    }
    if (this.authMethod === 'otp' && this.otpSent) {
      return 'Verify & Login';
    }
    return 'Sign In';
  }

  onSubmit() {
    this.submitted = true;
    this.showAlert = false;

    // Stop here if form is invalid
    if (this.loginForm.invalid) {
      return;
    }

    this.loading = true;

    if (this.authMethod === 'otp') {
      this.handleOtpFlow();
    } else {
      this.handlePasswordLogin();
    }
  }

  private handlePasswordLogin() {
    this.authService.login(this.loginForm.value).subscribe({
      next: () => {
        this.handleSuccessfulAuth();
      },
      error: (error) => {
        this.handleError(error);
      },
    });
  }

  private handleOtpFlow() {
    if (!this.otpSent) {
      // Send OTP
      const identifier = this.loginForm.get('identifier')?.value;

      // Check if identifier is a phone number (contains only digits and +)
      const isPhoneNumber = /^[\+]?[0-9\s\-\(\)]+$/.test(identifier);

      if (!isPhoneNumber) {
        this.handleError({
          message: 'OTP authentication is only available for phone numbers.',
        });
        return;
      }

      // For now, show message that OTP is temporarily disabled
      this.handleError({
        message:
          'OTP authentication is temporarily unavailable. Please use password login.',
      });

      // TODO: Implement OTP sending when backend API is ready
      // this.authService.sendPhoneOtp(identifier, 'recaptcha-token').subscribe({
      //   next: (response) => {
      //     this.otpRequestId = response.sessionInfo;
      //     this.otpSent = true;
      //     this.updateValidators();
      //     this.loading = false;
      //     this.showSuccessAlert('OTP sent to your phone number');
      //   },
      //   error: (error) => {
      //     this.handleError(error);
      //   }
      // });
    } else {
      // Verify OTP
      const otp = this.loginForm.get('otp')?.value;

      // TODO: Implement OTP verification when backend API is ready
      // this.authService.verifyPhoneOtp(this.otpRequestId, otp).subscribe({
      //   next: () => {
      //     this.handleSuccessfulAuth();
      //   },
      //   error: (error) => {
      //     this.handleError(error);
      //   }
      // });
    }
  }

  private handleSuccessfulAuth() {
    this.loading = false;
    this.handlePostAuthRouting();
  }

  private handlePostAuthRouting() {
    console.log('=== LOGIN: Starting post-auth routing ===');

    // First, try to get user data from localStorage (from login response)
    const basicUser = this.authService.getCurrentUser();
    console.log('LOGIN: Basic user from localStorage:', basicUser);

    if (!basicUser) {
      console.log('LOGIN: No basic user data found, going to dashboard');
      this.router.navigate(['/dashboard']);
      return;
    }

    // Try to refresh user data for onboarding check, but handle organization users differently
    this.userDataService.refreshUserData().subscribe({
      next: (completeUser) => {
        console.log('=== LOGIN: Fresh user data loaded successfully ===');
        this.processUserRoutingWithTypeCheck(completeUser);
      },
      error: (error) => {
        console.warn(
          'LOGIN: Error loading complete user data (likely CORS):',
          error
        );

        // Get raw user data to check type for fallback routing
        const rawUserData = this.authService.getRawUserData();

        if (rawUserData && rawUserData.type === 'organization') {
          console.log(
            'LOGIN: Organization user detected in error fallback - using login response data'
          );
          this.handleOrganizationUserFallback(rawUserData);
        } else {
          console.log(
            'LOGIN: Individual user in error fallback - using existing CORS handling'
          );
          this.handleIndividualUserCorsError(error, basicUser);
        }
      },
    });
  }

  private processUserRoutingWithTypeCheck(completeUser: any) {
    if (!completeUser) {
      console.log('LOGIN: No complete user data found, going to dashboard');
      this.router.navigate(['/dashboard']);
      return;
    }

    // Log detailed profile information
    console.log('LOGIN: User profile details:', {
      userId: completeUser._id,
      type: completeUser.type,
      hasProfile: !!completeUser.profile,
      profile: completeUser.profile,
    });

    // Check if onboarding is completed using fresh data (ORIGINAL ONBOARDING CHECK)
    const isOnboardingComplete =
      this.authService.isOnboardingCompletedForUser(completeUser);
    console.log('LOGIN: Onboarding completion result:', isOnboardingComplete);

    if (!isOnboardingComplete) {
      // Redirect to onboarding if not completed
      console.log('LOGIN: Onboarding not completed, redirecting to onboarding');
      this.router.navigate(['/onboarding']);
      return;
    }

    // Onboarding is complete - now route based on user type
    console.log('LOGIN: Onboarding completed, routing based on user type');

    if (completeUser.type === 'organization') {
      console.log(
        'LOGIN: Organization user with completed onboarding - using login response for subdomain routing'
      );
      this.handleOrganizationUserWithCompleteOnboarding(completeUser);
    } else {
      console.log(
        'LOGIN: Individual user with completed onboarding - using standard routing'
      );
      this.routeBasedOnUserType(completeUser);
    }
  }

  private handleOrganizationUserWithCompleteOnboarding(completeUser: any) {
    console.log(
      '=== LOGIN: Processing organization user with completed onboarding ==='
    );

    // Get raw user data from login response for subdomain info
    const rawUserData = this.authService.getRawUserData();
    const targetOrg =
      rawUserData?.defaultOrganization || completeUser.defaultOrganization;

    console.log('LOGIN: Target organization for routing:', targetOrg);

    if (targetOrg && targetOrg.subdomain) {
      console.log('LOGIN: Organization user routing with subdomain:', {
        subdomain: targetOrg.subdomain,
        orgName: targetOrg.name,
        enableSubdomains: environment.enableSubdomains,
      });

      this.routeOrganizationUserToSubdomain(targetOrg);
    } else {
      // Organization user without subdomain - this is an error condition
      console.error('CRITICAL: Organization user has no subdomain!');
      console.log(
        'Organization users MUST have a subdomain. Redirecting to main dashboard as emergency fallback.'
      );
      this.router.navigate(['/dashboard']);
    }
  }

  private handleOrganizationUserFallback(rawUserData: any) {
    console.log(
      '=== LOGIN: Processing organization user fallback (CORS error) ==='
    );

    // For organization users in CORS error situation, we need to check onboarding
    // but we can't get complete user data, so we'll use basic checks

    // Use the dedicated method to check organization onboarding from login data
    const isOnboardingComplete =
      this.authService.isOrganizationOnboardingCompletedFromLoginData(
        rawUserData
      );

    if (!isOnboardingComplete) {
      // No default organization means onboarding is not completed
      console.log(
        'LOGIN: Organization user onboarding not completed (fallback), redirecting to onboarding'
      );
      this.router.navigate(['/onboarding']);
      return;
    }

    // Organization user with default organization - route to subdomain using login response
    const targetOrg = rawUserData.defaultOrganization;
    console.log(
      'LOGIN: Target organization from login response (fallback):',
      targetOrg
    );

    console.log('LOGIN: Organization user routing with subdomain (fallback):', {
      subdomain: targetOrg.subdomain,
      orgName: targetOrg.name,
      enableSubdomains: environment.enableSubdomains,
    });

    this.routeOrganizationUserToSubdomain(targetOrg);
  }

  private handleIndividualUserCorsError(error: any, basicUser: any) {
    console.log(
      'LOGIN: Handling individual user CORS error with existing logic'
    );

    // Check if this is a CORS error and try to extract subdomain
    const corsSubdomain = this.parseSubdomainFromCorsError(error);

    if (corsSubdomain) {
      console.log(
        `LOGIN: CORS error detected with subdomain: ${corsSubdomain}`
      );
      this.redirectToExtractedSubdomain(corsSubdomain);
    } else if (error.message && error.message.includes('CORS')) {
      console.log(
        'LOGIN: CORS error detected but no subdomain found - using fallback'
      );
      this.handleCorsErrorRouting(basicUser);
    } else {
      console.log('LOGIN: Non-CORS error - using fallback routing');
      this.handleFallbackRouting(basicUser);
    }
  }

  private routeOrganizationUserToSubdomain(targetOrg: any) {
    console.log('=== LOGIN: Routing organization user to subdomain ===');

    // Check if user is currently on a subdomain
    const currentHost = window.location.host;
    const isOnSubdomain = this.isCurrentlyOnSubdomain(currentHost);

    console.log('LOGIN: Subdomain routing analysis:', {
      currentHost,
      isOnSubdomain,
      targetSubdomain: targetOrg.subdomain,
      enableSubdomains: environment.enableSubdomains,
    });

    if (environment.enableSubdomains) {
      // Production: Use actual subdomains
      const subdomainHost = environment.subdomainPattern.replace(
        '{subdomain}',
        targetOrg.subdomain
      );

      if (currentHost === subdomainHost) {
        console.log(
          'LOGIN: Already on correct subdomain, routing to org-dashboard'
        );
        this.router.navigate(['/org-dashboard']);
      } else {
        // Check if user is on a different subdomain they don't have access to
        if (isOnSubdomain) {
          const currentSubdomain = this.extractSubdomainFromHost(currentHost);
          // For organization users, we only check if they have access to the current subdomain
          // Since we're using login response data, we assume they have access to their default org
          if (currentSubdomain !== targetOrg.subdomain) {
            console.log(
              '🚨 SECURITY: Organization user on different subdomain - redirecting to their subdomain'
            );
          }
        }

        console.log(`LOGIN: Redirecting to user's subdomain: ${subdomainHost}`);
        this.redirectToSubdomainWithTokens(subdomainHost, targetOrg.subdomain);
      }
    } else {
      // Development: Use path-based routing on same domain
      console.log(
        'LOGIN: Development mode - routing to organization dashboard on main domain'
      );
      this.router.navigate(['/org-dashboard']);
    }
  }

  private processUserRouting(completeUser: any) {
    if (!completeUser) {
      console.log('LOGIN: No complete user data found, going to dashboard');
      this.router.navigate(['/dashboard']);
      return;
    }

    // Log detailed profile information
    console.log('LOGIN: User profile details:', {
      userId: completeUser._id,
      type: completeUser.type,
      hasProfile: !!completeUser.profile,
      profile: completeUser.profile,
    });

    // Check if onboarding is completed using fresh data
    const isOnboardingComplete =
      this.authService.isOnboardingCompletedForUser(completeUser);
    console.log('LOGIN: Onboarding completion result:', isOnboardingComplete);

    if (!isOnboardingComplete) {
      // Redirect to onboarding if not completed
      console.log('LOGIN: Onboarding not completed, redirecting to onboarding');
      this.router.navigate(['/onboarding']);
      return;
    }

    // User has completed onboarding, route based on type and organization
    console.log('LOGIN: Onboarding completed, routing based on user type');
    try {
      this.routeBasedOnUserType(completeUser);
    } catch (error) {
      console.error('LOGIN: Error in routing logic:', error);
      // Fallback to standard dashboard
      this.router.navigate(['/dashboard']);
    }
  }

  private handleCorsErrorRouting(basicUser: any) {
    console.log('LOGIN: Handling CORS error routing with basic user data');

    // For CORS errors, we can extract the subdomain from the CORS error message
    // The API returns CORS headers for the user's actual subdomain

    // Try to extract subdomain from the CORS error by making a test API call
    this.extractSubdomainFromCorsError()
      .then((subdomain) => {
        if (subdomain) {
          console.log(
            `LOGIN: Extracted subdomain from CORS error: ${subdomain}`
          );
          this.redirectToExtractedSubdomain(subdomain);
        } else {
          console.log(
            'LOGIN: Could not extract subdomain, using fallback routing'
          );
          this.handleFallbackRoutingForCors(basicUser);
        }
      })
      .catch((error) => {
        console.error('LOGIN: Error extracting subdomain:', error);
        this.handleFallbackRoutingForCors(basicUser);
      });
  }

  private async extractSubdomainFromCorsError(): Promise<string | null> {
    return new Promise((resolve) => {
      console.log('LOGIN: Attempting to extract subdomain from CORS error...');

      // Make a test API call to trigger the CORS error
      this.userDataService.refreshUserData().subscribe({
        next: (_data) => {
          // If this succeeds, we're not in a CORS situation
          console.log(
            'LOGIN: API call succeeded, no CORS error to extract from'
          );
          resolve(null);
        },
        error: (error) => {
          console.log(
            'LOGIN: API call failed, checking for CORS error:',
            error
          );

          // Try to extract subdomain from error message
          const subdomain = this.parseSubdomainFromCorsError(error);
          resolve(subdomain);
        },
      });

      // Timeout after 5 seconds
      setTimeout(() => {
        console.log('LOGIN: Timeout waiting for CORS error');
        resolve(null);
      }, 5000);
    });
  }

  private parseSubdomainFromCorsError(error: any): string | null {
    try {
      // Look for CORS error message in various places
      let errorMessage = '';

      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.error?.message) {
        errorMessage = error.error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      console.log('LOGIN: Parsing CORS error message:', errorMessage);

      // Look for pattern: "Access-Control-Allow-Origin' header has a value 'https://subdomain.domain.com'"
      const corsPattern =
        /Access-Control-Allow-Origin.*?value\s+['"`]https?:\/\/([^.]+)\.([^'"`\s]+)['"`]/i;
      const match = errorMessage.match(corsPattern);

      if (match && match[1]) {
        const subdomain = match[1];
        console.log(`LOGIN: Successfully extracted subdomain: ${subdomain}`);
        return subdomain;
      }

      // Alternative pattern: look for subdomain.domain.com in the error
      const altPattern = /https?:\/\/([^.]+)\.digimeet\.live/i;
      const altMatch = errorMessage.match(altPattern);

      if (altMatch && altMatch[1]) {
        const subdomain = altMatch[1];
        console.log(
          `LOGIN: Successfully extracted subdomain (alt pattern): ${subdomain}`
        );
        return subdomain;
      }

      console.log('LOGIN: Could not extract subdomain from CORS error');
      return null;
    } catch (parseError) {
      console.error('LOGIN: Error parsing CORS error:', parseError);
      return null;
    }
  }

  private redirectToExtractedSubdomain(subdomain: string) {
    console.log(`LOGIN: Redirecting to extracted subdomain: ${subdomain}`);

    if (!environment.enableSubdomains) {
      console.log('LOGIN: Subdomains disabled, routing to org-dashboard');
      this.router.navigate(['/org-dashboard']);
      return;
    }

    const subdomainHost = environment.subdomainPattern.replace(
      '{subdomain}',
      subdomain
    );

    // Immediate redirect without delay
    console.log(`LOGIN: Immediate redirect to subdomain: ${subdomainHost}`);
    this.redirectToSubdomainWithTokens(subdomainHost, subdomain);
  }

  private handleFallbackRoutingForCors(_basicUser: any) {
    console.log('LOGIN: Using fallback routing for CORS error');

    // Check if we have stored complete user data from a previous session
    const storedCompleteUser = this.authService.getCompleteUser();

    if (storedCompleteUser && storedCompleteUser.type === 'organization') {
      console.log(
        'LOGIN: Found stored organization user data, using for routing'
      );
      this.processUserRouting(storedCompleteUser);
    } else {
      console.log(
        'LOGIN: No stored complete user data, using default dashboard'
      );
      this.router.navigate(['/dashboard']);
    }
  }

  private handleFallbackRouting(_basicUser: any) {
    console.log('LOGIN: Using fallback routing with basic user data');

    // Use basic user data to make routing decisions
    // This is a simplified version without complete user data

    const storedCompleteUser = this.authService.getCompleteUser();
    if (storedCompleteUser) {
      console.log(
        'LOGIN: Using stored complete user data for fallback routing'
      );
      this.processUserRouting(storedCompleteUser);
    } else {
      console.log('LOGIN: No stored data available, using default dashboard');
      this.router.navigate(['/dashboard']);
    }
  }

  private routeBasedOnUserType(completeUser: any) {
    console.log('=== ROUTING DEBUG ===');
    console.log('Environment:', {
      production: environment.production,
      enableSubdomains: environment.enableSubdomains,
      subdomainPattern: environment.subdomainPattern,
      appDomain: environment.appDomain,
    });
    console.log('Current host:', window.location.host);
    console.log('User type:', completeUser.type);
    console.log('User organizations:', completeUser.organizations);

    // Check if user is currently on a subdomain
    const currentHost = window.location.host;
    const isOnSubdomain = this.isCurrentlyOnSubdomain(currentHost);

    console.log('Subdomain analysis:', {
      currentHost,
      isOnSubdomain,
      enableSubdomains: environment.enableSubdomains,
    });

    if (completeUser.type === 'individual') {
      console.log('Processing individual user routing...');

      // Individual users should NEVER be on organization subdomains
      if (isOnSubdomain && environment.enableSubdomains) {
        console.log(
          '🚨 SECURITY: Individual user detected on subdomain - redirecting to 404'
        );
        this.router.navigate(['/404']);
        return;
      }

      // Individual users with completed onboarding go to standard dashboard
      console.log('Routing individual user to standard dashboard');
      this.router.navigate(['/dashboard']);
    } else if (completeUser.type === 'organization') {
      console.log('Processing organization user routing...');

      // Prioritize default organization, then fall back to first organization
      let targetOrg = completeUser.defaultOrganization;

      if (
        !targetOrg &&
        completeUser.organizations &&
        completeUser.organizations.length > 0
      ) {
        targetOrg = completeUser.organizations[0];
      }

      if (targetOrg) {
        if (targetOrg.subdomain) {
          console.log('Organization user routing:', {
            subdomain: targetOrg.subdomain,
            orgName: targetOrg.name,
            isDefaultOrg: !!completeUser.defaultOrganization,
            enableSubdomains: environment.enableSubdomains,
            environment: environment.production ? 'production' : 'development',
          });

          if (environment.enableSubdomains) {
            // Production: Use actual subdomains
            const subdomainHost = environment.subdomainPattern.replace(
              '{subdomain}',
              targetOrg.subdomain
            );

            if (currentHost === subdomainHost) {
              console.log(
                'Already on correct subdomain, routing to org-dashboard'
              );
              this.router.navigate(['/org-dashboard']);
            } else {
              // Check if user is on a different subdomain they don't have access to
              if (isOnSubdomain) {
                const currentSubdomain =
                  this.extractSubdomainFromHost(currentHost);
                const hasAccessToCurrentSubdomain =
                  this.userHasAccessToSubdomain(completeUser, currentSubdomain);

                if (!hasAccessToCurrentSubdomain) {
                  console.log(
                    '🚨 SECURITY: User on unauthorized subdomain - redirecting to 404'
                  );
                  this.router.navigate(['/404']);
                  return;
                }
              }

              console.log(`Redirecting to user's subdomain: ${subdomainHost}`);
              this.redirectToSubdomainWithTokens(
                subdomainHost,
                targetOrg.subdomain
              );
            }
          } else {
            // Development: Use path-based routing on same domain
            console.log(
              'Development mode: Using organization dashboard on main domain'
            );
            this.router.navigate(['/org-dashboard']);
          }
        } else {
          // Organization user without subdomain - this is an error condition
          console.error('CRITICAL: Organization user has no subdomain!');
          console.log(
            'Organization users MUST have a subdomain. Redirecting to main dashboard as emergency fallback.'
          );
          this.router.navigate(['/dashboard']);
        }
      } else {
        // Organization user but no organizations found, go to standard dashboard
        console.log(
          'Organization user with no organizations, routing to standard dashboard'
        );
        this.router.navigate(['/dashboard']);
      }
    } else {
      // Default fallback for unknown user types
      console.log('Unknown user type, routing to standard dashboard');

      // If on subdomain with unknown user type, redirect to 404
      if (isOnSubdomain && environment.enableSubdomains) {
        console.log('Unknown user type on subdomain - redirecting to 404');
        this.router.navigate(['/404']);
        return;
      }

      this.router.navigate(['/dashboard']);
    }
  }

  private redirectToSubdomainWithTokens(
    subdomainHost: string,
    subdomain: string
  ) {
    // Get current auth tokens
    const accessToken = this.authService.getAccessToken();
    const refreshToken = this.authService.getRefreshToken();

    if (!accessToken || !refreshToken) {
      console.error('No auth tokens available for subdomain redirect');
      this.handleError({
        message: 'Authentication tokens not found. Please login again.',
      });
      return;
    }

    console.log('Redirecting to subdomain with tokens:', {
      subdomain,
      subdomainHost,
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
    });

    // Create subdomain URL with tokens as query parameters
    const protocol = window.location.protocol;
    const subdomainUrl = `${protocol}//${subdomainHost}/auth/subdomain-login?token=${encodeURIComponent(
      accessToken
    )}&refresh=${encodeURIComponent(refreshToken)}&redirect=org-dashboard`;

    console.log('Final subdomain URL:', subdomainUrl);
    window.location.href = subdomainUrl;
  }

  /**
   * Check if the current host is a subdomain
   */
  private isCurrentlyOnSubdomain(host: string): boolean {
    if (!environment.enableSubdomains) {
      return false;
    }

    // Extract the base domain from the subdomain pattern
    // e.g., if pattern is "{subdomain}.digimeet.live", base domain is "digimeet.live"
    const baseDomain = environment.subdomainPattern.replace('{subdomain}.', '');

    // If current host is exactly the base domain, it's not a subdomain
    if (host === baseDomain || host === `www.${baseDomain}`) {
      return false;
    }

    // If current host ends with the base domain but is not the base domain, it's a subdomain
    return host.endsWith(`.${baseDomain}`) && host !== baseDomain;
  }

  /**
   * Extract subdomain from host
   */
  private extractSubdomainFromHost(host: string): string | null {
    if (!environment.enableSubdomains) {
      return null;
    }

    const baseDomain = environment.subdomainPattern.replace('{subdomain}.', '');

    if (host === baseDomain || host === `www.${baseDomain}`) {
      return null;
    }

    if (host.endsWith(`.${baseDomain}`)) {
      return host.replace(`.${baseDomain}`, '');
    }

    return null;
  }

  /**
   * Check if user has access to a specific subdomain
   */
  private userHasAccessToSubdomain(
    completeUser: any,
    subdomain: string | null
  ): boolean {
    if (!subdomain || !completeUser) {
      return false;
    }

    // Individual users should never have access to any subdomain
    if (completeUser.type === 'individual') {
      return false;
    }

    // Organization users should only have access to their organization subdomains
    if (completeUser.type === 'organization') {
      const userOrgs = [
        ...(completeUser.organizations || []),
        ...(completeUser.defaultOrganization
          ? [completeUser.defaultOrganization]
          : []),
      ];

      return userOrgs.some((org: any) => org.subdomain === subdomain);
    }

    return false;
  }

  private handleError(error: any) {
    this.loading = false;
    this.alertMessage = error.message || 'An error occurred. Please try again.';
    this.alertType = 'error';
    this.showAlert = true;
  }
}
